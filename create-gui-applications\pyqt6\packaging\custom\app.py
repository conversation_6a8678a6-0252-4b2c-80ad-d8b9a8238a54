from PyQt6.QtWidgets import Q<PERSON>ainWindow, QApplication, QPushButton
from PyQt6.QtGui import QIcon

import sys


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("Hello World")

        button = QPushButton("My simple app.")
        button.pressed.connect(self.close)

        self.setCentralWidget(button)


app = QApplication(sys.argv)
app.setWindowIcon(QIcon("icon.svg"))
window = MainWindow()
window.show()
app.exec()
