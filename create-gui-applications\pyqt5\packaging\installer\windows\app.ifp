﻿
[Header]
ProjectFileVersion = 1.1
[General]
Program name = Hello World
Program version = 1.0
Windows 2000 = 0
Windows XP = 1
Windows Server 2003 = 1
Windows Vista = 1
Windows Server 2008 = 1
Windows 7 = 1
Windows 8 = 1
Windows 10 = 1
Windows Server 2016 = 1
DoNotCheckOS = 0
Company name = Python GUIs
Website = https://www.pythonguis.com
SFA = 0
DFA = 1
Comp = 1
[Graphics]
Wizard image = <main>
Header image = <main>
Show Label = 1
VisualStylesEnabled = 1
[Files]
Installation path = <ProgramFiles>\<Company>\<AppName>\
Autcip = 1
[Uninstall]
Vwau = 0
Website = https://
Include uninstaller = 1
Uninstaller filename = Uninstall
UseCustomDisplayIcon = 0
CustomDisplayIcon = <InstallPath>\
[Licence]
Licence dialog = 0
[Finish]
Sart program = 1
Reboot computer = 0
Program = <InstallPath>\hello-world.exe
ProgramArguments = 
[Shortcuts]
Allowtc = 0
Shortcut path = <Company>\<AppName>
[Serialoptions]
Allows = 0
Number = 1000
Mask = #####-#####-#####-#####
[SplashScreen]
Image = 
Sound = 
Time = 2
PlaySound = 0
Allow = 0
[Build]
File = 
SetupIconPath = ..\icons\ancient.ico
UninstallIconPath = ..\icons\ancient.ico
CompressionMethod = 0
CompressionLevel = 2
[Updater]
Allow = 0
1 = <AppName>
2 = <AppVersion>
3 = http://
4 = http://
5 = http://
6 = Update
Language = 0
RunProg = 
RunProgs = 0
Execdlls = 0
[Languages]
[Files/Dirs]
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\_bz2.pyd
87 KB
pyd
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\_ctypes.pyd
131 KB
pyd
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\_hashlib.pyd
38 KB
pyd
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\_lzma.pyd
251 KB
pyd
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\_queue.pyd
27 KB
pyd
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\_socket.pyd
74 KB
pyd
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\_ssl.pyd
121.5 KB
pyd
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\_win32sysloader.pyd
12 KB
pyd
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-console-l1-1-0.dll
12 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-datetime-l1-1-0.dll
11.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-debug-l1-1-0.dll
11.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-errorhandling-l1-1-0.dll
11.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-file-l1-1-0.dll
15 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-file-l1-2-0.dll
11.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-file-l2-1-0.dll
11.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-handle-l1-1-0.dll
11.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-heap-l1-1-0.dll
12 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-interlocked-l1-1-0.dll
11.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-libraryloader-l1-1-0.dll
12.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-localization-l1-2-0.dll
14.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-memory-l1-1-0.dll
12 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-namedpipe-l1-1-0.dll
11.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-processenvironment-l1-1-0.dll
12.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-processthreads-l1-1-0.dll
14 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-processthreads-l1-1-1.dll
12 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-profile-l1-1-0.dll
11.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-rtlsupport-l1-1-0.dll
12 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-string-l1-1-0.dll
11.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-synch-l1-1-0.dll
13.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-synch-l1-2-0.dll
12 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-sysinfo-l1-1-0.dll
12.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-timezone-l1-1-0.dll
12 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-core-util-l1-1-0.dll
11.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-crt-conio-l1-1-0.dll
12.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-crt-convert-l1-1-0.dll
15.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-crt-environment-l1-1-0.dll
12 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-crt-filesystem-l1-1-0.dll
13.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-crt-heap-l1-1-0.dll
12.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-crt-locale-l1-1-0.dll
12 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-crt-math-l1-1-0.dll
20.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-crt-process-l1-1-0.dll
12.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-crt-runtime-l1-1-0.dll
16 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-crt-stdio-l1-1-0.dll
17.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-crt-string-l1-1-0.dll
18 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-crt-time-l1-1-0.dll
14 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\api-ms-win-crt-utility-l1-1-0.dll
12 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\base_library.zip
759.8 KB
zip
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\d3dcompiler_47.dll
4 MB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\hello-world.exe
1.7 MB
exe
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\libcrypto-1_1.dll
3.2 MB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\libEGL.dll
24.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\libGLESv2.dll
3.2 MB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\libssl-1_1.dll
670 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\MSVCP140.dll
552.4 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\MSVCP140_1.dll
31 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\opengl32sw.dll
20 MB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\pyexpat.pyd
194.5 KB
pyd
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\python3.dll
57.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\python37.dll
3.6 MB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\pywintypes37.dll
137.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\Qt5Core.dll
5.7 MB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\Qt5DBus.dll
426.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\Qt5Gui.dll
6.7 MB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\Qt5Network.dll
1.3 MB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\Qt5Qml.dll
3.4 MB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\Qt5QmlModels.dll
428.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\Qt5Quick.dll
4 MB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\Qt5Svg.dll
323 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\Qt5WebSockets.dll
146 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\Qt5Widgets.dll
5.2 MB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\select.pyd
26 KB
pyd
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\ucrtbase.dll
1011.5 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\unicodedata.pyd
1 MB
pyd
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\VCRUNTIME140.dll
87.6 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\VCRUNTIME140_1.dll
36.4 KB
dll
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\win32api.pyd
131 KB
pyd
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\win32evtlog.pyd
73 KB
pyd
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\icons\
N/A
[Folder]
U:\home\martin\books\create-simple-gui-applications\code\SOURCE\packaging\installer\windows\dist\hello-world\PyQt5\
N/A
[Folder]
[Licence_Begin]
114
{\rtf1\ansi\ansicpg1252\deff0\deflang1043{\fonttbl{\f0\fnil\fcharset0 Arial;}}
\viewkind4\uc1\pard\fs20\par
}
 [Licence_End]
[Registry]
[Variables]
[SCs]
Startmenu
Hello World
<InstallPath>\hello-world.exe


0
[IFP_End]
[Serials]
[Serials_End]
[Commands]
