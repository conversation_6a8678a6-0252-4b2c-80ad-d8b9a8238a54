# Resource object code (Python 3)
# Created by: object code
# Created by: The Resource Compiler for Qt version 6.6.2
# WARNING! All changes made in this file will be lost!

from PySide6 import QtCore

qt_resource_data = b"\
\x00\x00\x065\
\x89\
PNG\x0d\x0a\x1a\x0a\x00\x00\x00\x0dIHDR\x00\
\x00\x00\x10\x00\x00\x00\x10\x08\x06\x00\x00\x00\x1f\xf3\xffa\
\x00\x00\x00\x19tEXtSoftware\
\x00Adobe ImageRead\
yq\xc9e<\x00\x00\x03iiTXtXML\
:com.adobe.xmp\x00\x00\
\x00\x00\x00<?xpacket beg\
in=\x22\xef\xbb\xbf\x22 id=\x22W5M\
0MpCehiHzreSzNTc\
zkc9d\x22?> <x:xmpm\
eta xmlns:x=\x22ado\
be:ns:meta/\x22 x:x\
mptk=\x22Adobe XMP \
Core 5.0-c060 61\
.134777, 2010/02\
/12-17:32:00    \
    \x22> <rdf:RDF \
xmlns:rdf=\x22http:\
//www.w3.org/199\
9/02/22-rdf-synt\
ax-ns#\x22> <rdf:De\
scription rdf:ab\
out=\x22\x22 xmlns:xmp\
Rights=\x22http://n\
s.adobe.com/xap/\
1.0/rights/\x22 xml\
ns:xmpMM=\x22http:/\
/ns.adobe.com/xa\
p/1.0/mm/\x22 xmlns\
:stRef=\x22http://n\
s.adobe.com/xap/\
1.0/sType/Resour\
ceRef#\x22 xmlns:xm\
p=\x22http://ns.ado\
be.com/xap/1.0/\x22\
 xmpRights:Marke\
d=\x22False\x22 xmpMM:\
DocumentID=\x22xmp.\
did:13108D24C31B\
11E0B363F65AD567\
8C1A\x22 xmpMM:Inst\
anceID=\x22xmp.iid:\
13108D23C31B11E0\
B363F65AD5678C1A\
\x22 xmp:CreatorToo\
l=\x22Adobe Photosh\
op CS3 Windows\x22>\
 <xmpMM:DerivedF\
rom stRef:instan\
ceID=\x22uuid:AC1F2\
E83324ADF11AAB8C\
5390D85B5B3\x22 stR\
ef:documentID=\x22u\
uid:C9D349664A3C\
DD11B08ABBBCFF17\
2156\x22/> </rdf:De\
scription> </rdf\
:RDF> </x:xmpmet\
a> <?xpacket end\
=\x22r\x22?> \x11`\x13\x00\x00\x02bID\
ATx\xda\xa4\x93\xcfk\x13Q\x10\xc7g\x7f4\xda\
\xb4\xc5\xc6\x18k#\xd8$5\x82\xf4\xe0\xc1\xb0\xa2 \
\x01\x11#\x06k\xc4\x83g/\x1ez\xf4\xe4\xa1\xd0\xf6\
 x\xf5\x22\xfe\xfc\x13\x04\x7f\x10P(x0\x08F\
\xd34\xd2\x18j\xa8\x1a\xa1\xa9\xc5&/\xabqw\xb3\
\xc9\xee:\xf3\xd8\xd8\x90\x83\x08\x0e|x\xef\xcd\xfb\xce\
\xbcy\xf3v\x05\xc7q\xe0\x7fL@\xeb\xf7I\xc8\x09\
$\x81L\xb8\xbe\x0a\xf2\x02y\x8dX\x7fK\xe0Ef\
\xa2\xd1h*\x99L\x1e\x0a\x06\x83\xa3\xe4\xacV\xab\x8d\
t:\xfd\xb1\x5c.?\xc6\xe5mD\xfbsZ_\x82\
\x99x<~u~~\xeeH8\x1c\x19f\xac.\x89\
\xa2 \xc5b\xb1\xa1D\xe2\xcc\xfejuc\xa2R\xa9\
\xd0\x9d\xdf\xf4\x96\xd0e*\x10\x08\xe4\x97\x96r\x1d4\
\x87(\x14\x0a\x9c\xee\x9a\xf6HC\xdan\x5c\xaf\xddH\
$\x12uUU\x9d\x88\xcf\xe7\xe8\xba\xee4\x9bM\x0e\
\xcd\xc9G{\xa4!m7Ht\xc7\x8b\xc8\x95\xb1\xb1\
\xbd#\x9dN\x1bV\xd6\xd7\xa1\xd5j\xc1\xd0f\x8aC\
s\xf2YV\x07HCZ7\x06d\xe4\x12rWQ\
\x14\xbf(\x8a`\x18\x06\x0f\x90$\x09>-.\xf2\xec\
\xc3)\x03t]\x03\xdb\xb6\x0152j\xc7\xb3\xd9\xec\
}*\x80\x12<B6\xd1\xf1dr\xf2\xe0n,\x99\
D \xcb2\xf8/3\x9e\x801\x86\xa7[\x9cV\xab\
\x0d\xa8\xa5k\xa4\x90\x8c\xec^!\x83\xa8\x9af\xf8j\
\xb5\x9a\xe0\xf1x\xc0\xe3\x19\xc0Su\xde(\xba\x96i\
\x12&\xa0\x86^Auc@\xeeib\x9e15X\
\xab\xb1\x1d^\xaf\x17\xab\xd8\xee2}\xad\xb6\xed`\xb0\
\x86\xd5\xa8&i\xfb\x9bHv\xefCq\x99\xe9\x85[\
\x8e\xf4\xeb3&\xf0`\xa0\xc4\x11\x84\x01 \x9f\x86{\
\xa4!m7\x88WP\x9c\xe3\xf3\xd0\xb3J\xa7}\xfa\
\xd8\x9a\x90\xcd,8\xbbb\xd7\x05\xef\x81\xb3|C\xfb\
\xfa\x1c\xd4\xdcM\xe7\x94b\x0b\xd7\xce\x99\xed\x0b\x11\x08\
\x91\x7fj\xc1\xcd\xa2\x84x\x92w\xed\x95\x93V\xe9\xc1\
xcvZd#\x83\xf0\xde-5O\xf3\xd9\xf3\x02\
+\xde\xd9\xd30\x96\x8f[\xa4UB\xdb\x15\x8cf\xbf\
\x80\xefm\x05V\x7f<|\x05\x1b\xdf\x00\xcc:<\x8d\
\xed\x83\x5c\xe9;\xd4Ht8\x00~\x939GK/\
\xb7\xa6\xeb\xc5-X\xfb\x09\xab\x18\x13\xa6\x07\xa2.\xed\
D\x06\xddQ\xf8\xc7\xbf\x98^\xc2@\xf4\xdf\x02\x0c\x00\
\x1b\x166\xbc\xe4\xebGz\x00\x00\x00\x00IEND\
\xaeB`\x82\
"

qt_resource_name = b"\
\x00\x05\
\x00o\xa6S\
\x00i\
\x00c\x00o\x00n\x00s\
\x00\x0b\
\x0b,\xde\x87\
\x00p\
\x00e\x00n\x00g\x00u\x00i\x00n\x00.\x00p\x00n\x00g\
"

qt_resource_struct = b"\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x01\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x02\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x10\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\
\x00\x00\x01\x8eQ\x18b\xeb\
"

def qInitResources():
    QtCore.qRegisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

def qCleanupResources():
    QtCore.qUnregisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

qInitResources()
